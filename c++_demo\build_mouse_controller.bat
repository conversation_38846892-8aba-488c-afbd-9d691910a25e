@echo off
echo 正在编译KMBox鼠标控制程序...

:: 设置编译器路径（如果需要的话）
:: set PATH=%PATH%;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.xx.xxxxx\bin\Hostx64\x64

:: 编译程序
cl /EHsc mouse_controller.cpp NetConfig\kmboxNet.cpp NetConfig\my_enc.cpp xbox.cpp /I"NetConfig" /link ws2_32.lib /out:mouse_controller.exe

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！生成了 mouse_controller.exe
    echo 运行程序请执行: mouse_controller.exe
) else (
    echo 编译失败！请检查编译环境
)

pause
