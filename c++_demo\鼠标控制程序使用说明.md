# KMBox鼠标控制程序使用说明

## 程序简介

这是一个专门为KMBoxNet硬件设备开发的鼠标控制程序，提供了完整的鼠标操作功能，包括移动、点击、监听等。

## 功能特性

### 1. 基础鼠标操作
- **鼠标移动测试**: 执行方形移动轨迹
- **鼠标点击测试**: 左键、右键、中键点击测试
- **鼠标滚轮测试**: 向上和向下滚动测试

### 2. 高级移动功能
- **自动轨迹移动**: 平滑移动到指定坐标
- **贝塞尔曲线移动**: 使用贝塞尔曲线实现自然轨迹
- **画圆测试**: 绘制圆形轨迹

### 3. 实用功能
- **连续点击测试**: 自定义次数和间隔的连续点击
- **鼠标监听**: 实时监听物理鼠标的操作状态

## 编译方法

### 方法1: 使用批处理文件（推荐）
```bash
build_mouse_controller.bat
```

### 方法2: 手动编译
```bash
cl /EHsc mouse_controller.cpp NetConfig\kmboxNet.cpp NetConfig\my_enc.cpp xbox.cpp /I"NetConfig" /link ws2_32.lib /out:mouse_controller.exe
```

## 使用步骤

### 1. 准备工作
- 确保KMBoxNet设备已连接到网络
- 记录设备的IP地址、端口号和MAC地址
- 编译程序生成可执行文件

### 2. 运行程序
```bash
mouse_controller.exe
```

### 3. 连接设备
程序启动后会要求输入：
- **IP地址**: 例如 `*************`
- **端口号**: 例如 `8808`
- **MAC地址**: 例如 `62587019`

### 4. 选择功能
连接成功后，程序会显示功能菜单：

```
========== KMBox鼠标控制程序 ==========
1. 鼠标移动测试
2. 鼠标点击测试
3. 鼠标滚轮测试
4. 自动轨迹移动
5. 贝塞尔曲线移动
6. 启动鼠标监听
7. 停止鼠标监听
8. 画圆测试
9. 连续点击测试
0. 退出程序
=======================================
```

## 功能详解

### 鼠标移动测试 (选项1)
- 执行预设的方形移动轨迹
- 演示基础的鼠标移动功能

### 鼠标点击测试 (选项2)
- 依次测试左键、右键、中键
- 每个按键点击3次，间隔200ms

### 鼠标滚轮测试 (选项3)
- 向上滚动5次，向下滚动5次
- 演示滚轮控制功能

### 自动轨迹移动 (选项4)
- 输入目标坐标和移动时间
- 程序会平滑移动到目标位置
- 显示实际耗时

### 贝塞尔曲线移动 (选项5)
- 输入终点坐标、两个控制点和移动时间
- 使用贝塞尔曲线算法生成自然轨迹
- 适合模拟人工鼠标移动

### 鼠标监听 (选项6/7)
- 实时监听物理鼠标的操作
- 显示按键状态、位置变化、滚轮操作
- 可以同时监听多个鼠标事件

### 画圆测试 (选项8)
- 绘制圆形轨迹
- 适合在画图软件中测试
- 演示复杂轨迹控制

### 连续点击测试 (选项9)
- 自定义点击次数和间隔
- 适合需要重复点击的场景
- 提供倒计时提示

## 注意事项

### 1. 网络连接
- 确保电脑和KMBox设备在同一网络
- 检查防火墙设置，可能需要关闭Windows防火墙
- 确认设备IP地址、端口和MAC地址正确

### 2. 监听功能
- 监听功能需要开启特定端口（默认1000）
- 如果无法监听，请检查防火墙设置
- 监听和控制可以同时进行

### 3. 坐标系统
- 坐标为相对移动，不是绝对坐标
- 移动范围受屏幕分辨率限制
- 建议先进行小范围测试

### 4. 性能考虑
- 连续操作时注意间隔时间
- 避免过于频繁的调用
- 监听功能会占用一定CPU资源

## 错误处理

### 常见错误码
- **连接失败**: 检查IP、端口、MAC地址
- **网络超时**: 检查网络连接和防火墙
- **设备忙碌**: 等待设备空闲后重试

### 故障排除
1. **无法连接设备**
   - 检查设备电源和网络连接
   - 确认IP地址是否正确
   - 尝试ping设备IP

2. **监听无响应**
   - 关闭Windows防火墙
   - 检查端口是否被占用
   - 重启程序重新连接

3. **移动不准确**
   - 检查坐标参数
   - 调整移动时间
   - 确认设备固件版本

## 扩展开发

程序基于kmboxNet SDK开发，可以根据需要添加更多功能：
- 键盘控制功能
- Xbox手柄模拟
- 自定义轨迹算法
- 配置文件保存
- 日志记录功能

## 技术支持

如有问题，请检查：
1. 设备连接状态
2. 网络配置
3. 防火墙设置
4. 程序日志输出
