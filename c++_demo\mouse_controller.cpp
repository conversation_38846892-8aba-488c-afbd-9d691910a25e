#include <iostream>
#include <windows.h>
#include <conio.h>
#include <thread>
#include <atomic>
#include "NetConfig/kmboxNet.h"
#include "NetConfig/HidTable.h"

using namespace std;

// 全局变量控制监听线程
atomic<bool> monitoring(false);
atomic<bool> should_exit(false);

// 鼠标监听线程函数
void mouse_monitor_thread() {
    cout << "鼠标监听已启动，按任意键停止监听..." << endl;
    cout << "监听状态：左键、右键、中键、侧键1、侧键2、滚轮" << endl;
    
    while (monitoring && !should_exit) {
        // 检测鼠标左键
        if (kmNet_monitor_mouse_left() == 1) {
            cout << "[监听] 鼠标左键按下" << endl;
            while (kmNet_monitor_mouse_left() == 1 && monitoring) {
                Sleep(1);
            }
            if (monitoring) cout << "[监听] 鼠标左键松开" << endl;
        }
        
        // 检测鼠标右键
        if (kmNet_monitor_mouse_right() == 1) {
            cout << "[监听] 鼠标右键按下" << endl;
            while (kmNet_monitor_mouse_right() == 1 && monitoring) {
                Sleep(1);
            }
            if (monitoring) cout << "[监听] 鼠标右键松开" << endl;
        }
        
        // 检测鼠标中键
        if (kmNet_monitor_mouse_middle() == 1) {
            cout << "[监听] 鼠标中键按下" << endl;
            while (kmNet_monitor_mouse_middle() == 1 && monitoring) {
                Sleep(1);
            }
            if (monitoring) cout << "[监听] 鼠标中键松开" << endl;
        }
        
        // 检测鼠标侧键1
        if (kmNet_monitor_mouse_side1() == 1) {
            cout << "[监听] 鼠标侧键1按下" << endl;
            while (kmNet_monitor_mouse_side1() == 1 && monitoring) {
                Sleep(1);
            }
            if (monitoring) cout << "[监听] 鼠标侧键1松开" << endl;
        }
        
        // 检测鼠标侧键2
        if (kmNet_monitor_mouse_side2() == 1) {
            cout << "[监听] 鼠标侧键2按下" << endl;
            while (kmNet_monitor_mouse_side2() == 1 && monitoring) {
                Sleep(1);
            }
            if (monitoring) cout << "[监听] 鼠标侧键2松开" << endl;
        }
        
        // 检测鼠标位置变化
        int x, y;
        if (kmNet_monitor_mouse_xy(&x, &y) == 0) {
            static int last_x = 0, last_y = 0;
            if (x != last_x || y != last_y) {
                cout << "[监听] 鼠标移动到: (" << x << ", " << y << ")" << endl;
                last_x = x;
                last_y = y;
            }
        }
        
        // 检测滚轮
        int wheel;
        if (kmNet_monitor_mouse_wheel(&wheel) == 0 && wheel != 0) {
            cout << "[监听] 鼠标滚轮: " << wheel << endl;
        }
        
        Sleep(10); // 避免CPU占用过高
    }
    cout << "鼠标监听已停止" << endl;
}

// 显示菜单
void show_menu() {
    cout << "\n========== KMBox鼠标控制程序 ==========" << endl;
    cout << "1. 鼠标移动测试" << endl;
    cout << "2. 鼠标点击测试" << endl;
    cout << "3. 鼠标滚轮测试" << endl;
    cout << "4. 自动轨迹移动" << endl;
    cout << "5. 贝塞尔曲线移动" << endl;
    cout << "6. 启动鼠标监听" << endl;
    cout << "7. 停止鼠标监听" << endl;
    cout << "8. 画圆测试" << endl;
    cout << "9. 连续点击测试" << endl;
    cout << "0. 退出程序" << endl;
    cout << "=======================================" << endl;
    cout << "请选择功能 (0-9): ";
}

// 鼠标移动测试
void test_mouse_move() {
    cout << "鼠标移动测试 - 将进行方形移动" << endl;
    
    // 方形移动
    cout << "向右移动..." << endl;
    for (int i = 0; i < 10; i++) {
        kmNet_mouse_move(50, 0);
        Sleep(50);
    }
    
    cout << "向下移动..." << endl;
    for (int i = 0; i < 10; i++) {
        kmNet_mouse_move(0, 50);
        Sleep(50);
    }
    
    cout << "向左移动..." << endl;
    for (int i = 0; i < 10; i++) {
        kmNet_mouse_move(-50, 0);
        Sleep(50);
    }
    
    cout << "向上移动..." << endl;
    for (int i = 0; i < 10; i++) {
        kmNet_mouse_move(0, -50);
        Sleep(50);
    }
    
    cout << "方形移动完成！" << endl;
}

// 鼠标点击测试
void test_mouse_click() {
    cout << "鼠标点击测试 - 左键、右键、中键各点击3次" << endl;
    
    // 左键点击
    cout << "左键点击测试..." << endl;
    for (int i = 0; i < 3; i++) {
        kmNet_mouse_left(1);  // 按下
        Sleep(100);
        kmNet_mouse_left(0);  // 松开
        Sleep(200);
        cout << "左键点击 " << (i + 1) << "/3" << endl;
    }
    
    Sleep(500);
    
    // 右键点击
    cout << "右键点击测试..." << endl;
    for (int i = 0; i < 3; i++) {
        kmNet_mouse_right(1);  // 按下
        Sleep(100);
        kmNet_mouse_right(0);  // 松开
        Sleep(200);
        cout << "右键点击 " << (i + 1) << "/3" << endl;
    }
    
    Sleep(500);
    
    // 中键点击
    cout << "中键点击测试..." << endl;
    for (int i = 0; i < 3; i++) {
        kmNet_mouse_middle(1);  // 按下
        Sleep(100);
        kmNet_mouse_middle(0);  // 松开
        Sleep(200);
        cout << "中键点击 " << (i + 1) << "/3" << endl;
    }
    
    cout << "点击测试完成！" << endl;
}

// 鼠标滚轮测试
void test_mouse_wheel() {
    cout << "鼠标滚轮测试 - 向上滚动5次，向下滚动5次" << endl;
    
    cout << "向上滚动..." << endl;
    for (int i = 0; i < 5; i++) {
        kmNet_mouse_wheel(3);  // 正值向上滚动
        Sleep(200);
        cout << "向上滚动 " << (i + 1) << "/5" << endl;
    }
    
    Sleep(500);
    
    cout << "向下滚动..." << endl;
    for (int i = 0; i < 5; i++) {
        kmNet_mouse_wheel(-3);  // 负值向下滚动
        Sleep(200);
        cout << "向下滚动 " << (i + 1) << "/5" << endl;
    }
    
    cout << "滚轮测试完成！" << endl;
}

// 自动轨迹移动
void test_auto_move() {
    int x, y, time_ms;
    cout << "自动轨迹移动测试" << endl;
    cout << "请输入目标位置 X坐标: ";
    cin >> x;
    cout << "请输入目标位置 Y坐标: ";
    cin >> y;
    cout << "请输入移动时间(毫秒): ";
    cin >> time_ms;
    
    cout << "开始移动到 (" << x << ", " << y << "), 耗时 " << time_ms << "ms" << endl;
    
    DWORD start_time = GetTickCount();
    int ret = kmNet_mouse_move_auto(x, y, time_ms);
    DWORD actual_time = GetTickCount() - start_time;
    
    if (ret == 0) {
        cout << "移动完成！实际耗时: " << actual_time << "ms" << endl;
    } else {
        cout << "移动失败，错误码: " << ret << endl;
    }
}

// 贝塞尔曲线移动
void test_bezier_move() {
    int x, y, x1, y1, x2, y2, time_ms;
    cout << "贝塞尔曲线移动测试" << endl;
    cout << "请输入终点 X坐标: ";
    cin >> x;
    cout << "请输入终点 Y坐标: ";
    cin >> y;
    cout << "请输入控制点1 X坐标: ";
    cin >> x1;
    cout << "请输入控制点1 Y坐标: ";
    cin >> y1;
    cout << "请输入控制点2 X坐标: ";
    cin >> x2;
    cout << "请输入控制点2 Y坐标: ";
    cin >> y2;
    cout << "请输入移动时间(毫秒): ";
    cin >> time_ms;

    cout << "开始贝塞尔曲线移动..." << endl;

    DWORD start_time = GetTickCount();
    int ret = kmNet_mouse_move_beizer(x, y, time_ms, x1, y1, x2, y2);
    DWORD actual_time = GetTickCount() - start_time;

    if (ret == 0) {
        cout << "贝塞尔移动完成！实际耗时: " << actual_time << "ms" << endl;
    } else {
        cout << "贝塞尔移动失败，错误码: " << ret << endl;
    }
}

// 画圆测试
void test_draw_circle() {
    cout << "画圆测试 - 将画一个圆形轨迹" << endl;
    cout << "请确保在画图软件中按住鼠标左键..." << endl;
    cout << "按任意键开始画圆...";
    _getch();

    const double PI = 3.141592654;
    int radius = 100;  // 圆的半径
    int steps = 360;   // 分成360步

    cout << "\n开始画圆..." << endl;

    for (int angle = 0; angle < steps; angle++) {
        double radian = angle * PI / 180.0;
        int x = (int)(radius * cos(radian));
        int y = (int)(radius * sin(radian));

        kmNet_mouse_move(x - (int)(radius * cos((angle-1) * PI / 180.0)),
                        y - (int)(radius * sin((angle-1) * PI / 180.0)));
        Sleep(10);
    }

    cout << "画圆完成！" << endl;
}

// 连续点击测试
void test_continuous_click() {
    int count, interval;
    cout << "连续点击测试" << endl;
    cout << "请输入点击次数: ";
    cin >> count;
    cout << "请输入点击间隔(毫秒): ";
    cin >> interval;

    cout << "将在3秒后开始连续点击..." << endl;
    for (int i = 3; i > 0; i--) {
        cout << i << "..." << endl;
        Sleep(1000);
    }

    cout << "开始连续点击！" << endl;

    for (int i = 0; i < count; i++) {
        kmNet_mouse_left(1);  // 按下
        Sleep(50);
        kmNet_mouse_left(0);  // 松开
        Sleep(interval);
        cout << "点击 " << (i + 1) << "/" << count << endl;
    }

    cout << "连续点击完成！" << endl;
}

int main() {
    cout << "========== KMBox鼠标控制程序 ==========" << endl;
    cout << "正在连接KMBox设备..." << endl;

    // 连接设备 - 请根据你的设备信息修改这些参数
    string ip, port, mac;
    cout << "请输入KMBox设备IP地址 (例如: *************): ";
    cin >> ip;
    cout << "请输入端口号 (例如: 8808): ";
    cin >> port;
    cout << "请输入MAC地址 (例如: 62587019): ";
    cin >> mac;

    int ret = kmNet_init((char*)ip.c_str(), (char*)port.c_str(), (char*)mac.c_str());

    if (ret != 0) {
        cout << "连接失败！错误码: " << ret << endl;
        cout << "请检查IP地址、端口和MAC地址是否正确" << endl;
        cout << "按任意键退出...";
        _getch();
        return -1;
    }

    cout << "连接成功！" << endl;

    int choice;
    thread monitor_thread;

    while (!should_exit) {
        show_menu();
        cin >> choice;

        switch (choice) {
        case 1:
            test_mouse_move();
            break;
        case 2:
            test_mouse_click();
            break;
        case 3:
            test_mouse_wheel();
            break;
        case 4:
            test_auto_move();
            break;
        case 5:
            test_bezier_move();
            break;
        case 6:
            if (!monitoring) {
                kmNet_monitor(1000);  // 启动监听，端口1000
                monitoring = true;
                monitor_thread = thread(mouse_monitor_thread);
            } else {
                cout << "监听已经在运行中..." << endl;
            }
            break;
        case 7:
            if (monitoring) {
                monitoring = false;
                if (monitor_thread.joinable()) {
                    monitor_thread.join();
                }
                cout << "监听已停止" << endl;
            } else {
                cout << "监听未启动..." << endl;
            }
            break;
        case 8:
            test_draw_circle();
            break;
        case 9:
            test_continuous_click();
            break;
        case 0:
            should_exit = true;
            if (monitoring) {
                monitoring = false;
                if (monitor_thread.joinable()) {
                    monitor_thread.join();
                }
            }
            cout << "程序退出中..." << endl;
            break;
        default:
            cout << "无效选择，请重新输入！" << endl;
            break;
        }

        if (!should_exit && choice != 6 && choice != 7) {
            cout << "\n按任意键继续...";
            _getch();
        }
    }

    cout << "感谢使用KMBox鼠标控制程序！" << endl;
    return 0;
}
