{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 115, "parent": 0}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "clean-all::@6890427a1f51a3e7e1df", "name": "clean-all", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/CMakeFiles/clean-all", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/6f0b5fc75d809a561576db1f6d218650/clean-all.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}