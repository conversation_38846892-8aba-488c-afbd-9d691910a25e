{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "lib/MinSizeRel/kmbox_core.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_compile_options", "add_compile_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 44, "parent": 0}, {"command": 1, "file": 0, "line": 88, "parent": 0}, {"command": 2, "file": 0, "line": 13, "parent": 0}, {"command": 3, "file": 0, "line": 15, "parent": 0}, {"command": 3, "file": 0, "line": 14, "parent": 0}, {"command": 4, "file": 0, "line": 22, "parent": 0}, {"command": 5, "file": 0, "line": 103, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG"}, {"backtrace": 3, "fragment": "/W3"}], "defines": [{"backtrace": 4, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 5, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 6, "path": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo"}, {"backtrace": 6, "path": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/NetConfig"}, {"backtrace": 7, "path": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "kmbox_core::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files/KMBoxMouseController"}}, "name": "kmbox_core", "nameOnDisk": "kmbox_core.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 4, 5, 6, 7]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "NetConfig/kmboxNet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "NetConfig/my_enc.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "xbox.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "NetConfig/kmboxNet.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "NetConfig/HidTable.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "NetConfig/my_enc.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "xbox.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "picture.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}