{"artifacts": [{"path": "bin/Release/calldemo.exe"}, {"path": "bin/Release/calldemo.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_compile_options", "add_compile_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 67, "parent": 0}, {"command": 1, "file": 0, "line": 84, "parent": 0}, {"command": 2, "file": 0, "line": 68, "parent": 0}, {"command": 2, "file": 0, "line": 54, "parent": 0}, {"command": 3, "file": 0, "line": 13, "parent": 0}, {"command": 4, "file": 0, "line": 15, "parent": 0}, {"command": 4, "file": 0, "line": 14, "parent": 0}, {"command": 5, "file": 0, "line": 22, "parent": 0}, {"command": 6, "file": 0, "line": 105, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG"}, {"backtrace": 5, "fragment": "/W3"}], "defines": [{"backtrace": 6, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 7, "define": "_CRT_SECURE_NO_WARNINGS"}], "includes": [{"backtrace": 8, "path": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo"}, {"backtrace": 8, "path": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/NetConfig"}, {"backtrace": 9, "path": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "kmbox_core::@6890427a1f51a3e7e1df"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "calldemo::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files/KMBoxMouseController"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 3, "fragment": "lib\\Release\\kmbox_core.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "winmm.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "calldemo", "nameOnDisk": "calldemo.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "calldemo.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}