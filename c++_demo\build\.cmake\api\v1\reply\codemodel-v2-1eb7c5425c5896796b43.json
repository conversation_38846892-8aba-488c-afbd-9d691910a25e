{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-633c94a1f3c1da281675.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "KMBoxMouseController", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-eae2f364e18dce30041c.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-4af6f52f99393b39ccf6.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "calldemo::@6890427a1f51a3e7e1df", "jsonFile": "target-calldemo-Debug-1dedaac417f0e8631fc9.json", "name": "calldemo", "projectIndex": 0}, {"directoryIndex": 0, "id": "clean-all::@6890427a1f51a3e7e1df", "jsonFile": "target-clean-all-Debug-35edd2d5b9c71c77b46b.json", "name": "clean-all", "projectIndex": 0}, {"directoryIndex": 0, "id": "kmbox_core::@6890427a1f51a3e7e1df", "jsonFile": "target-kmbox_core-Debug-d70a4dfef99e4b3e905b.json", "name": "kmbox_core", "projectIndex": 0}, {"directoryIndex": 0, "id": "mouse_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-mouse_controller-Debug-cc34766e5b7f406101d6.json", "name": "mouse_controller", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-80903423b662705b4a11.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "KMBoxMouseController", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-eae2f364e18dce30041c.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-4af6f52f99393b39ccf6.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "calldemo::@6890427a1f51a3e7e1df", "jsonFile": "target-calldemo-Release-46ba811ce379aaff2733.json", "name": "calldemo", "projectIndex": 0}, {"directoryIndex": 0, "id": "clean-all::@6890427a1f51a3e7e1df", "jsonFile": "target-clean-all-Release-35edd2d5b9c71c77b46b.json", "name": "clean-all", "projectIndex": 0}, {"directoryIndex": 0, "id": "kmbox_core::@6890427a1f51a3e7e1df", "jsonFile": "target-kmbox_core-Release-ef3e60ac2ddb8d11d98a.json", "name": "kmbox_core", "projectIndex": 0}, {"directoryIndex": 0, "id": "mouse_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-mouse_controller-Release-5e979ef0c86d78389a72.json", "name": "mouse_controller", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-2c21bc6baa11548177f3.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "KMBoxMouseController", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-eae2f364e18dce30041c.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-4af6f52f99393b39ccf6.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "calldemo::@6890427a1f51a3e7e1df", "jsonFile": "target-calldemo-MinSizeRel-976c7e84d52a39556a49.json", "name": "calldemo", "projectIndex": 0}, {"directoryIndex": 0, "id": "clean-all::@6890427a1f51a3e7e1df", "jsonFile": "target-clean-all-MinSizeRel-35edd2d5b9c71c77b46b.json", "name": "clean-all", "projectIndex": 0}, {"directoryIndex": 0, "id": "kmbox_core::@6890427a1f51a3e7e1df", "jsonFile": "target-kmbox_core-MinSizeRel-03b997bd95583bab9c6c.json", "name": "kmbox_core", "projectIndex": 0}, {"directoryIndex": 0, "id": "mouse_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-mouse_controller-MinSizeRel-367c3adc1410910a8836.json", "name": "mouse_controller", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-f0cae3af46d64e603c2e.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "KMBoxMouseController", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-eae2f364e18dce30041c.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-4af6f52f99393b39ccf6.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "calldemo::@6890427a1f51a3e7e1df", "jsonFile": "target-calldemo-RelWithDebInfo-a41035935ae171bad0e8.json", "name": "calldemo", "projectIndex": 0}, {"directoryIndex": 0, "id": "clean-all::@6890427a1f51a3e7e1df", "jsonFile": "target-clean-all-RelWithDebInfo-35edd2d5b9c71c77b46b.json", "name": "clean-all", "projectIndex": 0}, {"directoryIndex": 0, "id": "kmbox_core::@6890427a1f51a3e7e1df", "jsonFile": "target-kmbox_core-RelWithDebInfo-b65f109539ca64a09680.json", "name": "kmbox_core", "projectIndex": 0}, {"directoryIndex": 0, "id": "mouse_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-mouse_controller-RelWithDebInfo-5e6c8b94461a564be97d.json", "name": "mouse_controller", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build", "source": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo"}, "version": {"major": 2, "minor": 4}}