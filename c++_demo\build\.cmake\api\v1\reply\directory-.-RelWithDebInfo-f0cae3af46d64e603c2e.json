{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 84, "parent": 0}, {"command": 0, "file": 0, "line": 88, "parent": 0}, {"command": 0, "file": 0, "line": 92, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["bin/RelWithDebInfo/mouse_controller.exe"], "targetId": "mouse_controller::@6890427a1f51a3e7e1df", "targetIndex": 5, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["bin/RelWithDebInfo/calldemo.exe"], "targetId": "calldemo::@6890427a1f51a3e7e1df", "targetIndex": 2, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "lib", "paths": ["lib/RelWithDebInfo/kmbox_core.lib"], "targetId": "kmbox_core::@6890427a1f51a3e7e1df", "targetIndex": 4, "type": "target"}, {"backtrace": 3, "component": "Unspecified", "destination": "include/kmbox", "paths": ["NetConfig/kmboxNet.h", "NetConfig/HidTable.h", "NetConfig/my_enc.h", "xbox.h", "picture.h"], "type": "file"}], "paths": {"build": ".", "source": "."}}