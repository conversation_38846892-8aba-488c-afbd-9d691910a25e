# KMBoxNet 鼠标控制程序

这是一个基于kmboxNet硬件设备的C++鼠标控制程序，提供了完整的鼠标操作功能。

## 项目结构

```
c++_demo/
├── NetConfig/              # KMBox核心库
│   ├── kmboxNet.h         # 主要API头文件
│   ├── kmboxNet.cpp       # 主要API实现
│   ├── HidTable.h         # HID键盘映射表
│   ├── my_enc.h           # 加密功能头文件
│   └── my_enc.cpp         # 加密功能实现
├── mouse_controller.cpp    # 鼠标控制程序 (新)
├── calldemo.cpp           # 原始演示程序
├── xbox.h/xbox.cpp        # Xbox手柄模拟
├── picture.h              # 图片数据
├── CMakeLists.txt         # CMake配置文件
├── build.bat              # Windows构建脚本
├── build.sh               # Linux/macOS构建脚本
└── doc/                   # 文档目录
```

## 功能特性

### 鼠标控制程序 (mouse_controller.cpp)
- ✅ 鼠标移动控制 (相对移动、自动轨迹、贝塞尔曲线)
- ✅ 鼠标点击控制 (左键、右键、中键、侧键)
- ✅ 鼠标滚轮控制
- ✅ 实时鼠标监听
- ✅ 连续点击功能
- ✅ 画圆轨迹演示
- ✅ 交互式菜单界面

### 原始演示程序 (calldemo.cpp)
- 完整的API功能演示
- Xbox手柄模拟
- 加密通信测试
- 性能测试
- LCD显示屏控制

## 编译方法

### 方法1: 使用构建脚本 (推荐)

**Windows:**
```bash
# Release版本
build.bat

# Debug版本
build.bat debug

# 清理构建文件
build.bat clean
```

**Linux/macOS:**
```bash
# Release版本
./build.sh

# Debug版本
./build.sh debug

# 清理构建文件
./build.sh clean

# 构建并安装
./build.sh install
```

### 方法2: 手动使用CMake

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Release

# 编译
cmake --build . --config Release

# 可执行文件在 build/bin/ 目录下
```

## 使用方法

### 1. 准备工作
- 确保KMBoxNet设备已连接到网络
- 记录设备的IP地址、端口号和MAC地址 (显示在设备屏幕上)

### 2. 运行程序
```bash
# Windows
cd build\bin\Release
mouse_controller.exe

# Linux/macOS
cd build/bin
./mouse_controller
```

### 3. 连接设备
程序启动后输入设备信息：
- IP地址: 例如 `*************`
- 端口号: 例如 `8808`
- MAC地址: 例如 `62587019`

### 4. 选择功能
连接成功后选择相应的功能进行测试。

## 依赖要求

### Windows
- Visual Studio 2017 或更高版本 (或 MinGW)
- CMake 3.10+
- Windows SDK (包含 Winsock2)

### Linux
- GCC 7+ 或 Clang 6+
- CMake 3.10+
- 开发工具包: `sudo apt-get install build-essential cmake`

### macOS
- Xcode Command Line Tools
- CMake 3.10+
- Homebrew (可选): `brew install cmake`

## 故障排除

### 连接问题
1. 检查设备IP地址、端口、MAC地址是否正确
2. 确保电脑和设备在同一网络
3. 关闭Windows防火墙或添加程序例外

### 编译问题
1. 确保安装了所需的编译工具
2. 检查CMake版本是否满足要求
3. 在Windows上确保安装了Windows SDK

### 监听功能问题
1. 关闭防火墙
2. 检查端口1000是否被占用
3. 以管理员权限运行程序

## 开发说明

相关开发协议文档见doc文件夹。

项目基于kmboxNet SDK开发，支持：
- 网络通信控制
- 硬件级鼠标键盘模拟
- 实时设备监听
- 加密通信防护
