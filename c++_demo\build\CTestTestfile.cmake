# CMake generated Testfile for 
# Source directory: C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo
# Build directory: C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if("${CTEST_CONFIGURATION_TYPE}" MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(connection_test "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin/Debug/mouse_controller.exe" "--test-connection")
  set_tests_properties(connection_test PROPERTIES  PASS_REGULAR_EXPRESSION "连接成功" TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;137;add_test;C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;0;")
elseif("${CTEST_CONFIGURATION_TYPE}" MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(connection_test "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin/Release/mouse_controller.exe" "--test-connection")
  set_tests_properties(connection_test PROPERTIES  PASS_REGULAR_EXPRESSION "连接成功" TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;137;add_test;C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;0;")
elseif("${CTEST_CONFIGURATION_TYPE}" MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(connection_test "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin/MinSizeRel/mouse_controller.exe" "--test-connection")
  set_tests_properties(connection_test PROPERTIES  PASS_REGULAR_EXPRESSION "连接成功" TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;137;add_test;C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;0;")
elseif("${CTEST_CONFIGURATION_TYPE}" MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(connection_test "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin/RelWithDebInfo/mouse_controller.exe" "--test-connection")
  set_tests_properties(connection_test PROPERTIES  PASS_REGULAR_EXPRESSION "连接成功" TIMEOUT "30" WORKING_DIRECTORY "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/bin" _BACKTRACE_TRIPLES "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;137;add_test;C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt;0;")
else()
  add_test(connection_test NOT_AVAILABLE)
endif()
