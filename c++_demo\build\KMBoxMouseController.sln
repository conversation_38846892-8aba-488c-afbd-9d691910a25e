﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{ED5A51F1-1994-32D3-AA30-45073A1FA908}"
	ProjectSection(ProjectDependencies) = postProject
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
		{695FD80E-C3E3-36E4-B829-6052FFCC1670} = {695FD80E-C3E3-36E4-B829-6052FFCC1670}
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC} = {29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1} = {61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{9AC1864A-71C2-331E-8329-E8FDF53E9F95}"
	ProjectSection(ProjectDependencies) = postProject
		{ED5A51F1-1994-32D3-AA30-45073A1FA908} = {ED5A51F1-1994-32D3-AA30-45073A1FA908}
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{3893B311-C7D4-3CA8-A8FF-B180C5F6D40C}"
	ProjectSection(ProjectDependencies) = postProject
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{E444F520-903D-39FC-9123-C55672B6DB51}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "calldemo", "calldemo.vcxproj", "{695FD80E-C3E3-36E4-B829-6052FFCC1670}"
	ProjectSection(ProjectDependencies) = postProject
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC} = {29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "clean-all", "clean-all.vcxproj", "{40660C6C-8C69-32BC-9584-93D9835860EC}"
	ProjectSection(ProjectDependencies) = postProject
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "kmbox_core", "kmbox_core.vcxproj", "{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}"
	ProjectSection(ProjectDependencies) = postProject
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "mouse_controller", "mouse_controller.vcxproj", "{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}"
	ProjectSection(ProjectDependencies) = postProject
		{E444F520-903D-39FC-9123-C55672B6DB51} = {E444F520-903D-39FC-9123-C55672B6DB51}
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC} = {29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.Debug|x64.ActiveCfg = Debug|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.Debug|x64.Build.0 = Debug|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.Release|x64.ActiveCfg = Release|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.Release|x64.Build.0 = Release|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{ED5A51F1-1994-32D3-AA30-45073A1FA908}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9AC1864A-71C2-331E-8329-E8FDF53E9F95}.Debug|x64.ActiveCfg = Debug|x64
		{9AC1864A-71C2-331E-8329-E8FDF53E9F95}.Release|x64.ActiveCfg = Release|x64
		{9AC1864A-71C2-331E-8329-E8FDF53E9F95}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9AC1864A-71C2-331E-8329-E8FDF53E9F95}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3893B311-C7D4-3CA8-A8FF-B180C5F6D40C}.Debug|x64.ActiveCfg = Debug|x64
		{3893B311-C7D4-3CA8-A8FF-B180C5F6D40C}.Release|x64.ActiveCfg = Release|x64
		{3893B311-C7D4-3CA8-A8FF-B180C5F6D40C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3893B311-C7D4-3CA8-A8FF-B180C5F6D40C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.Debug|x64.ActiveCfg = Debug|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.Debug|x64.Build.0 = Debug|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.Release|x64.ActiveCfg = Release|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.Release|x64.Build.0 = Release|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E444F520-903D-39FC-9123-C55672B6DB51}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.Debug|x64.ActiveCfg = Debug|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.Debug|x64.Build.0 = Debug|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.Release|x64.ActiveCfg = Release|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.Release|x64.Build.0 = Release|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{695FD80E-C3E3-36E4-B829-6052FFCC1670}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{40660C6C-8C69-32BC-9584-93D9835860EC}.Debug|x64.ActiveCfg = Debug|x64
		{40660C6C-8C69-32BC-9584-93D9835860EC}.Release|x64.ActiveCfg = Release|x64
		{40660C6C-8C69-32BC-9584-93D9835860EC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{40660C6C-8C69-32BC-9584-93D9835860EC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.Debug|x64.ActiveCfg = Debug|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.Debug|x64.Build.0 = Debug|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.Release|x64.ActiveCfg = Release|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.Release|x64.Build.0 = Release|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.Debug|x64.ActiveCfg = Debug|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.Debug|x64.Build.0 = Debug|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.Release|x64.ActiveCfg = Release|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.Release|x64.Build.0 = Release|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{61E33FCD-8D9A-3D3F-8C75-421BB9E086F1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {43B74AA4-104B-3DB0-83DB-F8AD732CB798}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
