cmake_minimum_required(VERSION 3.10)

# 项目名称和版本
project(KMBoxMouseController VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    # Visual Studio编译器选项
    add_compile_options(/W3)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(WIN32_LEAN_AND_MEAN)
else()
    # GCC/Clang编译器选项
    add_compile_options(-Wall -Wextra)
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/NetConfig
)

# 源文件列表
set(KMBOX_SOURCES
    NetConfig/kmboxNet.cpp
    NetConfig/my_enc.cpp
    xbox.cpp
)

# 头文件列表
set(KMBOX_HEADERS
    NetConfig/kmboxNet.h
    NetConfig/HidTable.h
    NetConfig/my_enc.h
    xbox.h
    picture.h
)

# 创建静态库 - KMBox核心库
add_library(kmbox_core STATIC ${KMBOX_SOURCES} ${KMBOX_HEADERS})

# 为库设置包含目录
target_include_directories(kmbox_core PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/NetConfig
)

# Windows特定的库链接
if(WIN32)
    target_link_libraries(kmbox_core 
        ws2_32      # Winsock2库
        winmm       # Windows多媒体库
    )
endif()

# 鼠标控制程序可执行文件
add_executable(mouse_controller mouse_controller.cpp)

# 链接KMBox核心库
target_link_libraries(mouse_controller kmbox_core)

# 原始演示程序可执行文件
add_executable(calldemo calldemo.cpp)
target_link_libraries(calldemo kmbox_core)

# 设置输出目录
set_target_properties(mouse_controller PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(calldemo PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(kmbox_core PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 安装规则
install(TARGETS mouse_controller calldemo
    RUNTIME DESTINATION bin
)

install(TARGETS kmbox_core
    ARCHIVE DESTINATION lib
)

install(FILES ${KMBOX_HEADERS}
    DESTINATION include/kmbox
)

# 创建一个配置头文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/config.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/config.h"
)

# 添加配置头文件到包含路径
target_include_directories(kmbox_core PUBLIC ${CMAKE_CURRENT_BINARY_DIR})
target_include_directories(mouse_controller PRIVATE ${CMAKE_CURRENT_BINARY_DIR})
target_include_directories(calldemo PRIVATE ${CMAKE_CURRENT_BINARY_DIR})

# 打印一些有用的信息
message(STATUS "KMBox Mouse Controller Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")

# 添加自定义目标用于清理
add_custom_target(clean-all
    COMMAND ${CMAKE_BUILD_TOOL} clean
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/bin
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/lib
    COMMENT "Cleaning all build artifacts"
)

# 添加文档目标（如果有Doxygen）
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    add_custom_target(docs
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Generating documentation with Doxygen"
        VERBATIM
    )
endif()

# 测试支持（可选）
enable_testing()

# 简单的连接测试
add_test(NAME connection_test
    COMMAND mouse_controller --test-connection
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 设置测试属性
set_tests_properties(connection_test PROPERTIES
    TIMEOUT 30
    PASS_REGULAR_EXPRESSION "连接成功"
)
