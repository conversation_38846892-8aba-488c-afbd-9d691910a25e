#!/bin/bash

# KMBox鼠标控制程序 - CMake构建脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认设置
BUILD_DIR="build"
BUILD_TYPE="Release"
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  debug    - 构建Debug版本 (默认: Release)"
    echo "  clean    - 清理构建目录"
    echo "  install  - 安装到系统"
    echo "  help     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0          - 构建Release版本"
    echo "  $0 debug    - 构建Debug版本"
    echo "  $0 clean    - 清理构建文件"
    echo "  $0 install  - 构建并安装"
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查构建依赖...${NC}"
    
    if ! command -v cmake &> /dev/null; then
        echo -e "${RED}错误: 未找到CMake，请先安装CMake${NC}"
        echo "Ubuntu/Debian: sudo apt-get install cmake"
        echo "CentOS/RHEL: sudo yum install cmake"
        echo "macOS: brew install cmake"
        exit 1
    fi
    
    if ! command -v make &> /dev/null && ! command -v ninja &> /dev/null; then
        echo -e "${RED}错误: 未找到构建工具 (make 或 ninja)${NC}"
        echo "Ubuntu/Debian: sudo apt-get install build-essential"
        echo "CentOS/RHEL: sudo yum groupinstall 'Development Tools'"
        echo "macOS: xcode-select --install"
        exit 1
    fi
    
    echo -e "${GREEN}依赖检查通过${NC}"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        clean)
            echo -e "${YELLOW}清理构建目录...${NC}"
            rm -rf "$BUILD_DIR"
            echo -e "${GREEN}清理完成！${NC}"
            exit 0
            ;;
        install)
            INSTALL_FLAG="--target install"
            shift
            ;;
        help|--help|-h)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

echo "========================================"
echo "KMBox鼠标控制程序 - CMake构建脚本"
echo "========================================"

# 检查依赖
check_dependencies

# 创建构建目录
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${BLUE}创建构建目录: $BUILD_DIR${NC}"
    mkdir -p "$BUILD_DIR"
fi

# 进入构建目录
cd "$BUILD_DIR"

echo -e "${BLUE}配置项目 (构建类型: $BUILD_TYPE)...${NC}"

# 检测构建系统
if command -v ninja &> /dev/null; then
    GENERATOR="-G Ninja"
    BUILD_TOOL="ninja"
else
    GENERATOR=""
    BUILD_TOOL="make"
fi

# 配置项目
cmake .. $GENERATOR -DCMAKE_BUILD_TYPE="$BUILD_TYPE"

echo -e "${BLUE}开始编译 (使用 $JOBS 个并行任务)...${NC}"

# 编译项目
if [ "$BUILD_TOOL" = "ninja" ]; then
    ninja -j$JOBS
else
    make -j$JOBS
fi

# 安装（如果指定）
if [ ! -z "$INSTALL_FLAG" ]; then
    echo -e "${BLUE}安装程序...${NC}"
    if [ "$BUILD_TOOL" = "ninja" ]; then
        ninja install
    else
        make install
    fi
fi

echo ""
echo "========================================"
echo -e "${GREEN}编译成功！${NC}"
echo "========================================"
echo "可执行文件位置:"
echo "  - 鼠标控制程序: $BUILD_DIR/bin/mouse_controller"
echo "  - 原始演示程序: $BUILD_DIR/bin/calldemo"
echo ""
echo "运行程序:"
echo "  cd $BUILD_DIR/bin"
echo "  ./mouse_controller"
echo "========================================"

# 返回原目录
cd ..

echo ""
echo -e "${GREEN}构建完成！${NC}"
