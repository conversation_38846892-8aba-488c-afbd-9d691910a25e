﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\kmboxNet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\my_enc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\xbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\kmboxNet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\HidTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\my_enc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\xbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\picture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{66C6CC2B-8F07-38FC-9A8C-67A7FA02FDE6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{FC577C22-F071-3802-B16F-AC2ED2B81DA6}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
