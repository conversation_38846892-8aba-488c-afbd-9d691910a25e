﻿  mouse_controller.cpp
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(103,1): warning C4005: “AF_IPX”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(457): message : 参见“AF_IPX”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(147,1): warning C4005: “AF_MAX”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(476): message : 参见“AF_MAX”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(187,1): warning C4005: “SO_DONTLINGER”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(399): message : 参见“SO_DONTLINGER”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(240,25): error C2011: “sockaddr”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(482): message : 参见“sockaddr”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(442,5): error C2143: 语法错误: 缺少“}”(在“常数”的前面)
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(442,5): error C2059: 语法错误:“常数”
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(496,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(496,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(496,20): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(577,1): warning C4005: “IN_CLASSA”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(284): message : 参见“IN_CLASSA”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(583,1): warning C4005: “IN_CLASSB”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(290): message : 参见“IN_CLASSB”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(589,1): warning C4005: “IN_CLASSC”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(296): message : 参见“IN_CLASSC”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(600,1): warning C4005: “INADDR_ANY”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(301): message : 参见“INADDR_ANY”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(602,1): warning C4005: “INADDR_BROADCAST”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(303): message : 参见“INADDR_BROADCAST”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\shared\ws2def.h(638,28): error C2011: “sockaddr_in”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(309): message : 参见“sockaddr_in”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(136,23): error C2011: “fd_set”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(65): message : 参见“fd_set”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(171,1): warning C4005: “FD_SET”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(99): message : 参见“FD_SET”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(180,16): error C2011: “timeval”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(108): message : 参见“timeval”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(236,17): error C2011: “hostent”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(164): message : 参见“hostent”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(249,16): error C2011: “netent”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(177): message : 参见“netent”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(256,17): error C2011: “servent”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(184): message : 参见“servent”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(268,18): error C2011: “protoent”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(196): message : 参见“protoent”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(364,24): error C2011: “WSAData”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(319): message : 参见“WSAData”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(462,18): error C2011: “sockproto”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(491): message : 参见“sockproto”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(504,16): error C2011: “linger”:“struct”类型重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(528): message : 参见“linger”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(517,1): warning C4005: “SOMAXCONN”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(541): message : 参见“SOMAXCONN”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(551,1): warning C4005: “FD_READ”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(559): message : 参见“FD_READ”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(554,1): warning C4005: “FD_WRITE”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(560): message : 参见“FD_WRITE”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(557,1): warning C4005: “FD_OOB”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(561): message : 参见“FD_OOB”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(560,1): warning C4005: “FD_ACCEPT”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(562): message : 参见“FD_ACCEPT”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(563,1): warning C4005: “FD_CONNECT”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(563): message : 参见“FD_CONNECT”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(566,1): warning C4005: “FD_CLOSE”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(564): message : 参见“FD_CLOSE”的前一个定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1631,1): error C2375: “accept”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(739): message : 参见“accept”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1653,1): error C2375: “bind”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(744): message : 参见“bind”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1674,1): error C2375: “closesocket”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(749): message : 参见“closesocket”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1691,1): error C2375: “connect”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(751): message : 参见“connect”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1712,1): error C2375: “ioctlsocket”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(756): message : 参见“ioctlsocket”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1735,1): error C2375: “getpeername”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(761): message : 参见“getpeername”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1756,1): error C2375: “getsockname”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(766): message : 参见“getsockname”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1777,1): error C2375: “getsockopt”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(771): message : 参见“getsockopt”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1802,1): error C2375: “htonl”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(778): message : 参见“htonl”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1819,1): error C2375: “htons”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(780): message : 参见“htons”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1837,1): error C2375: “inet_addr”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(782): message : 参见“inet_addr”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1855,1): error C2375: “inet_ntoa”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(784): message : 参见“inet_ntoa”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1955,1): error C2375: “listen”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(786): message : 参见“listen”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1974,1): error C2375: “ntohl”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(790): message : 参见“ntohl”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(1991,1): error C2375: “ntohs”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(792): message : 参见“ntohs”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2008,1): error C2375: “recv”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(794): message : 参见“recv”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2031,1): error C2375: “recvfrom”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(800): message : 参见“recvfrom”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2058,1): error C2375: “select”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(808): message : 参见“select”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2083,1): error C2375: “send”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(815): message : 参见“send”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2106,1): error C2375: “sendto”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(821): message : 参见“sendto”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2133,1): error C2375: “setsockopt”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(829): message : 参见“setsockopt”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2158,1): error C2375: “shutdown”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(836): message : 参见“shutdown”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2178,1): error C2375: “socket”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(840): message : 参见“socket”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2203,1): error C2375: “gethostbyaddr”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(847): message : 参见“gethostbyaddr”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2225,1): error C2375: “gethostbyname”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(852): message : 参见“gethostbyname”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2242,1): error C2375: “gethostname”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(854): message : 参见“gethostname”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2282,1): error C2375: “getservbyport”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(858): message : 参见“getservbyport”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2301,1): error C2375: “getservbyname”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(862): message : 参见“getservbyname”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2320,1): error C2375: “getprotobynumber”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(866): message : 参见“getprotobynumber”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2337,1): error C2375: “getprotobyname”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(868): message : 参见“getprotobyname”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2357,1): error C2375: “WSAStartup”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(872): message : 参见“WSAStartup”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2377,1): error C2375: “WSACleanup”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(876): message : 参见“WSACleanup”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2394,1): error C2375: “WSASetLastError”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(878): message : 参见“WSASetLastError”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2411,1): error C2375: “WSAGetLastError”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(880): message : 参见“WSAGetLastError”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2432,1): error C2375: “WSAIsBlocking”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(882): message : 参见“WSAIsBlocking”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2450,1): error C2375: “WSAUnhookBlockingHook”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(884): message : 参见“WSAUnhookBlockingHook”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2468,1): error C2375: “WSASetBlockingHook”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(886): message : 参见“WSASetBlockingHook”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2486,1): error C2375: “WSACancelBlockingCall”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(888): message : 参见“WSACancelBlockingCall”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2504,1): error C2375: “WSAAsyncGetServByName”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(890): message : 参见“WSAAsyncGetServByName”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2532,1): error C2375: “WSAAsyncGetServByPort”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(898): message : 参见“WSAAsyncGetServByPort”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2560,1): error C2375: “WSAAsyncGetProtoByName”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(906): message : 参见“WSAAsyncGetProtoByName”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2586,1): error C2375: “WSAAsyncGetProtoByNumber”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(913): message : 参见“WSAAsyncGetProtoByNumber”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2612,1): error C2375: “WSAAsyncGetHostByName”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(920): message : 参见“WSAAsyncGetHostByName”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2638,1): error C2375: “WSAAsyncGetHostByAddr”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(927): message : 参见“WSAAsyncGetHostByAddr”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2668,1): error C2375: “WSACancelAsyncRequest”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(936): message : 参见“WSACancelAsyncRequest”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(2686,1): error C2375: “WSAAsyncSelect”: 重定义；不同的链接
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winsock.h(938): message : 参见“WSAAsyncSelect”的声明
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(4318,1): error C2059: 语法错误:“}”
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\Winsock2.h(4318,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\HidTable.h(119,1): warning C4005: “KEY_EXECUTE”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winnt.h(23195): message : 参见“KEY_EXECUTE”的前一个定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(97,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(98,5): error C2146: 语法错误: 缺少“;”(在标识符“cout”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(102,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(103,5): error C2146: 语法错误: 缺少“;”(在标识符“cout”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(109,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(112,5): error C2146: 语法错误: 缺少“;”(在标识符“cout”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(136,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(137,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(141,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(144,5): error C2146: 语法错误: 缺少“;”(在标识符“cout”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(177,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(178,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(182,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(184,5): error C2146: 语法错误: 缺少“;”(在标识符“cout”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(198,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(199,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(226,19): error C2365: “y1”: 重定义；以前的定义是“函数”
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\ucrt\corecrt_math.h(989): message : 参见“y1”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227,10): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227,13): error C2001: 常量中有换行符
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(229,9): error C2143: 语法错误: 缺少“;”(在“>>”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(229,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(230,10): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(230,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(230,5): error C2086: “int cout”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227): message : 参见“cout”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(231,9): error C2143: 语法错误: 缺少“;”(在“>>”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(231,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(231,5): error C2086: “int cin”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(229): message : 参见“cin”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(232,10): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(232,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(232,5): error C2086: “int cout”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227): message : 参见“cout”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(233,9): error C2143: 语法错误: 缺少“;”(在“>>”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(233,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(233,5): error C2086: “int cin”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(229): message : 参见“cin”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(234,10): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(234,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(234,5): error C2086: “int cout”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227): message : 参见“cout”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(235,9): error C2143: 语法错误: 缺少“;”(在“>>”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(235,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(235,5): error C2086: “int cin”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(229): message : 参见“cin”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(236,10): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(236,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(236,5): error C2086: “int cout”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227): message : 参见“cout”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(237,9): error C2143: 语法错误: 缺少“;”(在“>>”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(237,9): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(237,5): error C2086: “int cin”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(229): message : 参见“cin”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(238,10): error C2143: 语法错误: 缺少“;”(在“<<”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(238,10): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(238,5): error C2086: “int cout”: 重定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(227): message : 参见“cout”的声明
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(239,9): error C2143: 语法错误: 缺少“;”(在“>>”的前面)
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\mouse_controller.cpp(239,9): fatal error C1003: 错误计数超过 100；正在停止编译
  kmboxNet.cpp
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\kmboxNet.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\HidTable.h(119,1): warning C4005: “KEY_EXECUTE”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winnt.h(23195): message : 参见“KEY_EXECUTE”的前一个定义
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\kmboxNet.cpp(739,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  xbox.cpp
C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\xbox.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  正在生成代码...
