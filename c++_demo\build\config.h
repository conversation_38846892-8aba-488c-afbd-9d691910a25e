#ifndef KMBOX_CONFIG_H
#define KMBOX_CONFIG_H

// 项目版本信息
#define KMBOX_VERSION_MAJOR 1
#define KMBOX_VERSION_MINOR 0
#define KMBOX_VERSION_PATCH 0
#define KMBOX_VERSION_STRING "1.0.0"

// 编译时间
#define KMBOX_BUILD_DATE __DATE__
#define KMBOX_BUILD_TIME __TIME__

// 平台检测
#ifdef _WIN32
    #define KMBOX_PLATFORM_WINDOWS 1
    #define KMBOX_PLATFORM_NAME "Windows"
#elif defined(__linux__)
    #define KMBOX_PLATFORM_LINUX 1
    #define KMBOX_PLATFORM_NAME "Linux"
#elif defined(__APPLE__)
    #define KMBOX_PLATFORM_MACOS 1
    #define KMBOX_PLATFORM_NAME "macOS"
#else
    #define KMBOX_PLATFORM_UNKNOWN 1
    #define KMBOX_PLATFORM_NAME "Unknown"
#endif

// 默认配置
#define KMBOX_DEFAULT_PORT "8808"
#define KMBOX_DEFAULT_MONITOR_PORT 1000
#define KMBOX_DEFAULT_TIMEOUT_MS 5000

// 调试选项
#ifdef _DEBUG
    #define KMBOX_DEBUG 1
#else
    #define KMBOX_DEBUG 0
#endif

#endif // KMBOX_CONFIG_H
