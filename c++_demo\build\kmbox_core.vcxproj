﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{29E23FF7-4D64-3B10-9C92-EC1D3F9A26FC}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>kmbox_core</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">kmbox_core.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">kmbox_core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">kmbox_core.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">kmbox_core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\lib\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">kmbox_core.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">kmbox_core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\lib\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">kmbox_core.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">kmbox_core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CRT_SECURE_NO_WARNINGS;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo -BC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build --check-stamp-file C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\config.h.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindDoxygen.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo -BC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build --check-stamp-file C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\config.h.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindDoxygen.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo -BC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build --check-stamp-file C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\config.h.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindDoxygen.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo -BC:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build --check-stamp-file C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\config.h.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindDoxygen.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\kmboxNet.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\my_enc.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\xbox.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\kmboxNet.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\HidTable.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\NetConfig\my_enc.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\xbox.h" />
    <ClInclude Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\picture.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\接收器\kmboxnet\c++_demo\build\ZERO_CHECK.vcxproj">
      <Project>{E444F520-903D-39FC-9123-C55672B6DB51}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>