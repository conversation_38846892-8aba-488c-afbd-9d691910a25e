﻿  calldemo.cpp
C:\Users\<USER>\Desktop\C_Code\kmboxnet\c++_demo\NetConfig\HidTable.h(119,1): warning C4005: “KEY_EXECUTE”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winnt.h(23195): message : 参见“KEY_EXECUTE”的前一个定义
  my_enc.cpp
  kmboxNet.cpp
C:\Users\<USER>\Desktop\C_Code\kmboxnet\c++_demo\NetConfig\HidTable.h(119,1): warning C4005: “KEY_EXECUTE”: 宏重定义
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22000.0\um\winnt.h(23195): message : 参见“KEY_EXECUTE”的前一个定义
  xbox.cpp
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_sendto
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_htons
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_recvfrom
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_socket
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_inet_addr
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_WSAStartup
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_closesocket
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_bind
kmboxNet.obj : error LNK2001: 无法解析的外部符号 __imp_WSACleanup
C:\Users\<USER>\Desktop\C_Code\kmboxnet\c++_demo\x64\Release\kmNetLib.exe : fatal error LNK1120: 9 个无法解析的外部命令
