{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.23.0/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.23.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.23.0/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}, {"path": "config.h.in"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindDoxygen.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo/build", "source": "C:/Users/<USER>/Desktop/接收器/kmboxnet/c++_demo"}, "version": {"major": 1, "minor": 0}}