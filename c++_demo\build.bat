@echo off
setlocal enabledelayedexpansion

echo ========================================
echo KMBox鼠标控制程序 - CMake构建脚本
echo ========================================

:: 设置构建目录
set BUILD_DIR=build
set BUILD_TYPE=Release

:: 解析命令行参数
:parse_args
if "%1"=="debug" (
    set BUILD_TYPE=Debug
    shift
    goto parse_args
)
if "%1"=="clean" (
    echo 清理构建目录...
    if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
    echo 清理完成！
    goto end
)
if "%1"=="help" (
    goto show_help
)
if "%1"=="/?" (
    goto show_help
)

:: 检查CMake是否安装
cmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到CMake，请先安装CMake
    echo 下载地址: https://cmake.org/download/
    goto error_exit
)

:: 创建构建目录
if not exist %BUILD_DIR% (
    echo 创建构建目录: %BUILD_DIR%
    mkdir %BUILD_DIR%
)

:: 进入构建目录
cd %BUILD_DIR%

echo 配置项目 (构建类型: %BUILD_TYPE%)...
cmake .. -DCMAKE_BUILD_TYPE=%BUILD_TYPE%

if %ERRORLEVEL% neq 0 (
    echo 错误: CMake配置失败
    goto error_exit
)

echo 开始编译...
cmake --build . --config %BUILD_TYPE%

if %ERRORLEVEL% neq 0 (
    echo 错误: 编译失败
    goto error_exit
)

echo.
echo ========================================
echo 编译成功！
echo ========================================
echo 可执行文件位置:
echo   - 鼠标控制程序: %BUILD_DIR%\bin\%BUILD_TYPE%\mouse_controller.exe
echo   - 原始演示程序: %BUILD_DIR%\bin\%BUILD_TYPE%\calldemo.exe
echo.
echo 运行程序:
echo   cd %BUILD_DIR%\bin\%BUILD_TYPE%
echo   mouse_controller.exe
echo ========================================

goto end

:show_help
echo 用法: build.bat [选项]
echo.
echo 选项:
echo   debug    - 构建Debug版本 (默认: Release)
echo   clean    - 清理构建目录
echo   help     - 显示此帮助信息
echo.
echo 示例:
echo   build.bat          - 构建Release版本
echo   build.bat debug    - 构建Debug版本
echo   build.bat clean    - 清理构建文件
goto end

:error_exit
cd ..
echo.
echo 构建失败！请检查错误信息。
pause
exit /b 1

:end
cd ..
if not "%1"=="clean" (
    echo.
    echo 按任意键退出...
    pause >nul
)
endlocal
